// API 通用响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T | null;
  msg: string;
}

// 分页参数
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// 分页响应
export interface PaginationResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  per_page: number;
}

// 错误码定义
export enum ErrorCode {
  // 通用错误码
  INVALID_PARAMS = 10001,
  UNAUTHORIZED = 10002,
  FORBIDDEN = 10003,
  NOT_FOUND = 10004,
  RATE_LIMIT = 10005,
  INTERNAL_ERROR = 10006,

  // 验证相关错误码
  LICENSE_NOT_FOUND = 20001,
  LICENSE_EXPIRED = 20002,
  LICENSE_REVOKED = 20003,
  DEVICE_LIMIT_EXCEEDED = 20004,
  FEATURE_DENIED = 20005,
  INVALID_DEVICE_FINGERPRINT = 20006,

  // 管理相关错误码
  INVALID_CREDENTIALS = 30001,
  USERNAME_EXISTS = 30002,
  PRODUCT_UNAUTHORIZED = 30003,
  PRODUCT_NOT_FOUND = 30004,
  LICENSE_NOT_FOUND_ADMIN = 30005,
  ORDER_NOT_FOUND = 30006,
}
