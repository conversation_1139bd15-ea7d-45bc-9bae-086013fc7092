{"name": "@verify/utils", "version": "0.0.1", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts"}, "dependencies": {"zod": "^4.0.14", "@verify/types": "workspace:*"}, "devDependencies": {"typescript": "^5.7.2"}}