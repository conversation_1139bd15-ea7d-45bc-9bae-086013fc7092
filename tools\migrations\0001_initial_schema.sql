-- 初始化数据库表结构
-- 创建时间: 2025-07-30

-- 管理员表
CREATE TABLE admins (
  id TEXT PRIMARY KEY,                    -- UUID格式的管理员ID
  username TEXT UNIQUE NOT NULL,          -- 用户名
  password_hash TEXT NOT NULL,            -- 密码哈希值
  role TEXT NOT NULL CHECK (role IN ('super', 'normal')), -- 角色：超级管理员/普通管理员
  created_by TEXT,                        -- 创建者ID，超级管理员为NULL
  authorized_products TEXT,               -- JSON数组，普通管理员可操作的产品ID列表
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'disabled')), -- 账号状态
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_login DATETIME,                    -- 最后登录时间
  FOREIGN KEY (created_by) REFERENCES admins(id)
);

-- 管理员表索引
CREATE INDEX idx_admins_username ON admins(username);
CREATE INDEX idx_admins_role ON admins(role);
CREATE INDEX idx_admins_created_by ON admins(created_by);

-- 软件产品表
CREATE TABLE products (
  id TEXT PRIMARY KEY,                    -- UUID格式的产品ID
  name TEXT NOT NULL,                     -- 产品名称
  description TEXT,                       -- 产品描述
  price DECIMAL(10,2) NOT NULL,           -- 产品价格
  download_url TEXT,                      -- 产品下载链接
  verification_strategies TEXT NOT NULL,  -- JSON格式的验证策略配置
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'disabled')), -- 产品状态
  created_by TEXT NOT NULL,               -- 创建者ID（超级管理员）
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES admins(id)
);

-- 产品表索引
CREATE INDEX idx_products_created_by ON products(created_by);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_name ON products(name);

-- 许可证表
CREATE TABLE licenses (
  id TEXT PRIMARY KEY,                    -- UUID格式的许可证ID
  license_key TEXT UNIQUE NOT NULL,       -- 许可证密钥
  product_id TEXT NOT NULL,               -- 关联产品ID
  admin_id TEXT NOT NULL,                 -- 生成者ID
  price DECIMAL(10,2) NOT NULL,           -- 销售价格
  expiry_date DATETIME,                   -- 过期时间，NULL表示永久许可证
  device_limit INTEGER DEFAULT 1,         -- 设备数量限制
  features TEXT,                          -- JSON数组，允许的功能列表
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'revoked', 'expired')), -- 许可证状态
  metadata TEXT,                          -- JSON格式的额外元数据
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  activated_at DATETIME,                  -- 首次激活时间
  last_verified DATETIME,                 -- 最后验证时间
  FOREIGN KEY (product_id) REFERENCES products(id),
  FOREIGN KEY (admin_id) REFERENCES admins(id)
);

-- 许可证表索引
CREATE INDEX idx_licenses_license_key ON licenses(license_key);
CREATE INDEX idx_licenses_product_id ON licenses(product_id);
CREATE INDEX idx_licenses_admin_id ON licenses(admin_id);
CREATE INDEX idx_licenses_status ON licenses(status);
CREATE INDEX idx_licenses_expiry_date ON licenses(expiry_date);
CREATE INDEX idx_licenses_created_at ON licenses(created_at);

-- 设备绑定表
CREATE TABLE devices (
  id TEXT PRIMARY KEY,                    -- UUID格式的设备ID
  license_id TEXT NOT NULL,               -- 关联许可证ID
  device_fingerprint TEXT NOT NULL,       -- 设备指纹
  device_info TEXT,                       -- JSON格式的设备信息
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'unbound')), -- 绑定状态
  first_seen DATETIME DEFAULT CURRENT_TIMESTAMP, -- 首次绑定时间
  last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 最后活跃时间
  FOREIGN KEY (license_id) REFERENCES licenses(id),
  UNIQUE(license_id, device_fingerprint)
);

-- 设备表索引
CREATE INDEX idx_devices_license_id ON devices(license_id);
CREATE INDEX idx_devices_fingerprint ON devices(device_fingerprint);
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_devices_last_seen ON devices(last_seen);

-- 验证日志表
CREATE TABLE verification_logs (
  id TEXT PRIMARY KEY,                    -- UUID格式的日志ID
  license_id TEXT NOT NULL,               -- 关联许可证ID
  device_fingerprint TEXT,                -- 设备指纹
  result TEXT NOT NULL CHECK (result IN ('success', 'expired', 'device_limit', 'feature_denied', 'revoked', 'not_found', 'invalid')), -- 验证结果
  requested_features TEXT,                -- JSON数组，请求的功能列表
  granted_features TEXT,                  -- JSON数组，授权的功能列表
  ip_address TEXT,                        -- 请求IP地址
  user_agent TEXT,                        -- 用户代理
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  response_time INTEGER,                  -- 响应时间（毫秒）
  FOREIGN KEY (license_id) REFERENCES licenses(id)
);

-- 验证日志表索引
CREATE INDEX idx_verification_logs_license_id ON verification_logs(license_id);
CREATE INDEX idx_verification_logs_timestamp ON verification_logs(timestamp);
CREATE INDEX idx_verification_logs_result ON verification_logs(result);
CREATE INDEX idx_verification_logs_device_fingerprint ON verification_logs(device_fingerprint);

-- 订单表
CREATE TABLE orders (
  id TEXT PRIMARY KEY,                    -- UUID格式的订单ID
  order_number TEXT UNIQUE NOT NULL,      -- 订单号（用户友好）
  license_id TEXT UNIQUE NOT NULL,        -- 关联许可证ID（1:1关系）
  admin_id TEXT NOT NULL,                 -- 销售员ID
  channel TEXT,                           -- 销售渠道（便于后续扩展）
  customer_name TEXT,                     -- 客户名称（便于后续扩展）
  remarks TEXT,                           -- 备注信息（便于后续扩展）
  amount DECIMAL(10,2) NOT NULL,          -- 订单金额
  status TEXT DEFAULT 'generated' CHECK (status IN ('generated', 'sold', 'refunded')), -- 销售状态
  refund_reason TEXT,                     -- 退款原因
  sold_at DATETIME,                       -- 确认售出时间
  refunded_at DATETIME,                   -- 退款时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (license_id) REFERENCES licenses(id),
  FOREIGN KEY (admin_id) REFERENCES admins(id)
);

-- 订单表索引
CREATE INDEX idx_orders_order_number ON orders(order_number);
CREATE INDEX idx_orders_license_id ON orders(license_id);
CREATE INDEX idx_orders_admin_id ON orders(admin_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_channel ON orders(channel);
CREATE INDEX idx_orders_customer_name ON orders(customer_name);
