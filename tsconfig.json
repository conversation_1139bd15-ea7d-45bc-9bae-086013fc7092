{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "paths": {"@verify/types": ["./packages/types/src"], "@verify/utils": ["./packages/utils/src"], "@verify/ui": ["./packages/ui/src"]}}, "include": [], "exclude": ["node_modules", "dist", "build"]}