{"name": "api", "version": "0.0.1", "private": true, "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "start": "wrangler dev", "cf-typegen": "wrangler types", "build": "tsc", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "test": "vitest"}, "dependencies": {"@hono/swagger-ui": "^0.5.2", "@hono/zod-openapi": "^1.0.2", "hono": "^4.6.20", "nanoid": "^5.1.5", "zod": "^4.0.14", "jose": "^5.0.0", "bcryptjs": "^2.4.3", "@verify/types": "workspace:*", "@verify/utils": "workspace:*"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241218.0", "@types/node": "22.13.0", "@types/bcryptjs": "^2.4.6", "typescript": "^5.7.2", "wrangler": "^4.26.1", "vitest": "^1.0.0"}}