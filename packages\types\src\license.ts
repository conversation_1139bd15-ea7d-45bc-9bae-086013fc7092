// 许可证相关类型定义

// 许可证状态
export enum LicenseStatus {
  ACTIVE = 'active',
  REVOKED = 'revoked',
  EXPIRED = 'expired'
}

// 验证结果
export enum VerificationResult {
  SUCCESS = 'success',
  EXPIRED = 'expired',
  DEVICE_LIMIT = 'device_limit',
  FEATURE_DENIED = 'feature_denied',
  REVOKED = 'revoked',
  NOT_FOUND = 'not_found',
  INVALID = 'invalid'
}

// 设备状态
export enum DeviceStatus {
  ACTIVE = 'active',
  UNBOUND = 'unbound'
}

// 许可证验证请求
export interface VerifyLicenseRequest {
  license_key: string;
  device_fingerprint?: string;
  requested_features?: string[];
  client_info?: {
    version?: string;
    platform?: string;
    ip?: string;
  };
}

// 许可证验证响应
export interface VerifyLicenseResponse {
  license_id: string;
  product_id: string;
  product_name: string;
  status: LicenseStatus;
  expiry_date: string | null;
  days_remaining: number;
  device_limit: number;
  current_devices: number;
  device_slots_available: number;
  features: {
    granted: string[];
    denied: string[];
    available: string[];
  };
  device_info?: {
    is_new_device: boolean;
    device_id: string;
    first_seen: string;
    last_seen: string;
  };
  verification_time: string;
}

// 设备解绑请求
export interface UnbindDeviceRequest {
  license_key: string;
  device_fingerprint: string;
}

// 设备解绑响应
export interface UnbindDeviceResponse {
  license_id: string;
  device_limit: number;
  current_devices: number;
  device_slots_available: number;
  unbound_device: string;
}
