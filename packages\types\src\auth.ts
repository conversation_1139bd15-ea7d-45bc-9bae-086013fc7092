// 认证相关类型定义

// 管理员角色
export enum AdminRole {
  SUPER = 'super',
  NORMAL = 'normal'
}

// 管理员状态
export enum AdminStatus {
  ACTIVE = 'active',
  DISABLED = 'disabled'
}

// JWT Token 载荷
export interface JWTPayload {
  admin_id: string;
  username: string;
  role: AdminRole;
  authorized_products?: string[];
  iat: number;
  exp: number;
}

// 登录请求
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应
export interface LoginResponse {
  token: string;
  admin_id: string;
  username: string;
  role: AdminRole;
  authorized_products?: string[];
  expires_in: number;
}

// Token 刷新响应
export interface RefreshTokenResponse {
  token: string;
  expires_in: number;
}
