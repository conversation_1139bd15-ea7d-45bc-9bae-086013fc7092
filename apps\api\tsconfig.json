{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["@cloudflare/workers-types", "@types/node"], "paths": {"@verify/types": ["../../packages/types/src"], "@verify/utils": ["../../packages/utils/src"]}}, "include": ["src/**/*", "worker-configuration.d.ts"], "exclude": ["node_modules", "dist"]}